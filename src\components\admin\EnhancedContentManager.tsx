import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { MediaItem } from "@/types/media";
import { ContentFilters, SortField, SortOrder } from "@/types/admin";
import ContentEditDialog from "./ContentEditDialog";
import apiService from "@/services/apiService";
import {
  Eye, Edit, Trash2, Search, Filter, ArrowUpDown,
  Star, Image, Plus, Settings, Download,
  ChevronLeft, ChevronRight
} from "lucide-react";
import { scrollToTop } from "@/utils/scrollToTop";
import { getDefaultCategory } from "@/utils/contentFilters";

export default function EnhancedContentManager() {
  const { toast } = useToast();
  
  // State management
  const [content, setContent] = useState<MediaItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<SortField>('createdAt');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [filters, setFilters] = useState<ContentFilters>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [isLoading, setIsLoading] = useState(true);
  const [totalItems, setTotalItems] = useState(0);

  // Dialog states
  const [selectedContent, setSelectedContent] = useState<MediaItem | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);

  // Load content from database
  const loadContent = async () => {
    setIsLoading(true);
    try {
      const result = await apiService.getContent({
        page: currentPage,
        limit: itemsPerPage,
        type: filters.type,
        category: filters.category,
        search: filters.search,
        published: filters.published,
      });

      if (result.success) {
        setContent(result.data);
        setTotalItems(result.total);
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to load content from database",
          variant: "destructive",
        });
        setContent([]);
        setTotalItems(0);
      }
    } catch (error) {
      console.error('Failed to load content:', error);
      toast({
        title: "Error",
        description: "Failed to load content from database",
        variant: "destructive",
      });
      setContent([]);
      setTotalItems(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Load content on component mount and when filters/pagination change
  useEffect(() => {
    loadContent();
  }, [currentPage, filters]);

  // Filtered and sorted content
  const filteredContent = useMemo(() => {
    let filtered = [...content];

    // Apply filters
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower) ||
        item.genres.some(genre => genre.toLowerCase().includes(searchLower))
      );
    }

    if (filters.type) {
      filtered = filtered.filter(item => item.type === filters.type);
    }

    if (filters.featured !== undefined) {
      filtered = filtered.filter(item => item.isFeatured === filters.featured);
    }

    if (filters.carousel !== undefined) {
      filtered = filtered.filter(item => item.addToCarousel === filters.carousel);
    }

    if (filters.year) {
      filtered = filtered.filter(item => item.year === filters.year);
    }

    if (filters.genre) {
      filtered = filtered.filter(item => 
        item.genres.some(genre => genre.toLowerCase().includes(filters.genre!.toLowerCase()))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy];
      let bValue: any = b[sortBy];

      // Handle special cases
      if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [content, filters, sortBy, sortOrder]);

  // Pagination (server-side)
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const paginatedContent = content; // Content is already paginated from server

  // Event handlers
  const handleSort = (field: SortField) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleEdit = (item: MediaItem) => {
    setSelectedContent(item);
    setIsEditDialogOpen(true);
  };

  const handleView = (item: MediaItem) => {
    setSelectedContent(item);
    setIsViewDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    const item = content.find(c => c.id === id);
    if (window.confirm(`Are you sure you want to delete "${item?.title}"? This action cannot be undone.`)) {
      try {
        const result = await apiService.deleteContent(id);

        if (result.success) {
          // Remove from local state
          setContent(prev => prev.filter(c => c.id !== id));
          setTotalItems(prev => prev - 1);

          toast({
            title: "Content deleted",
            description: `"${item?.title}" has been deleted successfully from database`,
          });
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to delete content from database",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Failed to delete content:', error);
        toast({
          title: "Error",
          description: "Failed to delete content from database",
          variant: "destructive",
        });
      }
    }
  };

  const handleSaveContent = async (updatedContent: MediaItem) => {
    try {
      // Convert MediaItem to ContentFormData format for API
      const formData = {
        title: updatedContent.title,
        type: updatedContent.type,
        category: updatedContent.category || "",
        tmdbId: updatedContent.tmdbId?.toString() || "",
        year: updatedContent.year?.toString() || "",
        genres: updatedContent.genres || [],
        languages: updatedContent.languages || [],
        description: updatedContent.description || "",
        posterUrl: updatedContent.image || "",
        thumbnailUrl: updatedContent.coverImage || "",
        videoLinks: updatedContent.videoLinks || "",
        secureVideoLinks: updatedContent.secureVideoLinks || "",
        quality: updatedContent.quality || [],
        tags: updatedContent.tags || "",
        imdbRating: updatedContent.imdbRating?.toString() || "",
        runtime: updatedContent.runtime?.toString() || "",
        studio: updatedContent.studio || "",
        audioTracks: updatedContent.audioTracks || [],
        trailer: updatedContent.trailer || "",
        subtitleFile: null,
        subtitleUrl: updatedContent.subtitleUrl || "",
        isPublished: updatedContent.isPublished ?? false,
        isFeatured: updatedContent.isFeatured ?? false,
        addToCarousel: updatedContent.addToCarousel ?? false,
      };

      const result = await apiService.updateContent(updatedContent.id, formData);

      if (result.success) {
        // Update local state
        setContent(prev => prev.map(item =>
          item.id === updatedContent.id ? updatedContent : item
        ));

        toast({
          title: "Content updated",
          description: `"${updatedContent.title}" has been updated successfully in database`,
        });
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to update content in database",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to update content:', error);
      toast({
        title: "Error",
        description: "Failed to update content in database",
        variant: "destructive",
      });
    }
  };

  const handleToggleFeature = async (id: string, field: 'isFeatured' | 'addToCarousel') => {
    const item = content.find(c => c.id === id);
    if (!item) return;

    const newValue = !item[field];

    try {
      // Update in database first
      const updateData = {
        title: item.title,
        type: item.type,
        category: item.category || "",
        section: item.section || "",
        tmdbId: item.tmdbId?.toString() || "",
        year: item.year?.toString() || "",
        genres: item.genres || [],
        languages: item.languages || [],
        description: item.description || "",
        posterUrl: item.image || "",
        thumbnailUrl: item.coverImage || "",
        videoLinks: item.videoLinks || "",
        secureVideoLinks: item.secureVideoLinks || "",
        quality: item.quality || [],
        tags: item.tags || "",
        imdbRating: item.imdbRating || "",
        runtime: item.runtime || "",
        studio: item.studio || "",
        audioTracks: item.audioTracks || [],
        trailer: item.trailer || "",
        subtitleUrl: item.subtitleUrl || "",
        isPublished: item.isPublished,
        isFeatured: field === 'isFeatured' ? newValue : item.isFeatured,
        addToCarousel: field === 'addToCarousel' ? newValue : item.addToCarousel,
      };

      const result = await apiService.updateContent(id, updateData);

      if (result.success) {
        // Update local state only after successful database update
        setContent(prev => prev.map(contentItem =>
          contentItem.id === id ? { ...contentItem, [field]: newValue, updatedAt: new Date().toISOString() } : contentItem
        ));

        toast({
          title: "Feature updated",
          description: `${field === 'isFeatured' ? 'Featured' : 'Carousel'} ${newValue ? 'enabled' : 'disabled'} for "${item.title}" and saved to database`,
        });
      } else {
        toast({
          title: "Error",
          description: result.message || `Failed to update ${field === 'isFeatured' ? 'featured' : 'carousel'} status`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error updating feature:', error);
      toast({
        title: "Error",
        description: `Failed to update ${field === 'isFeatured' ? 'featured' : 'carousel'} status`,
        variant: "destructive",
      });
    }
  };

  const toggleSelectAll = async () => {
    if (selectedItems.length > 0) {
      setSelectedItems([]);
    } else {
      // For "Select All", we need to fetch all content IDs from the server
      try {
        const response = await fetch('/api/admin/content?all_ids=true', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          const allIds = await response.json();
          setSelectedItems(allIds);
        } else {
          // Fallback to current page if API call fails
          setSelectedItems(paginatedContent.map(item => item.id));
        }
      } catch (error) {
        console.error('Error fetching all content IDs:', error);
        // Fallback to current page if API call fails
        setSelectedItems(paginatedContent.map(item => item.id));
      }
    }
  };

  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedItems.length} selected items? This action cannot be undone.`)) {
      try {
        const result = await apiService.bulkContentOperation('delete', selectedItems);

        if (result.success) {
          // Reload content from database
          await loadContent();
          setSelectedItems([]);

          toast({
            title: "Bulk delete completed",
            description: `${result.results?.totalDeleted || 0} items have been deleted from database`,
          });
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to delete items from database",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Bulk delete failed:', error);
        toast({
          title: "Error",
          description: "Failed to delete items from database",
          variant: "destructive",
        });
      }
    }
  };

  // Function to handle page changes with scroll to top
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    scrollToTop();
  };

  // Helper function to escape CSV values and handle special characters
  const escapeCsvValue = (value: any): string => {
    if (value === null || value === undefined) return '';
    const stringValue = String(value);
    // Escape quotes by doubling them and wrap in quotes if contains comma, quote, or newline
    if (stringValue.includes('"') || stringValue.includes(',') || stringValue.includes('\n') || stringValue.includes('\r')) {
      return `"${stringValue.replace(/"/g, '""')}"`;
    }
    return stringValue;
  };

  const exportData = async (format: 'csv' | 'json') => {
    let dataToExport;

    if (selectedItems.length > 0) {
      // If items are selected, fetch all selected content from server
      try {
        const response = await fetch(`/api/admin/content?ids=${selectedItems.join(',')}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          dataToExport = await response.json();
        } else {
          // Fallback to current page content
          dataToExport = content.filter(item => selectedItems.includes(item.id));
        }
      } catch (error) {
        console.error('Error fetching selected content:', error);
        // Fallback to current page content
        dataToExport = content.filter(item => selectedItems.includes(item.id));
      }
    } else {
      dataToExport = filteredContent;
    }

    if (format === 'csv') {
      // Comprehensive CSV headers including all content fields - matches bulk import template
      const csvHeaders = [
        'Title', 'Description', 'Type', 'Category', 'Section IDs', 'Year', 'Genres', 'Languages', 'Status',
        'Featured', 'Carousel', 'IMDb Rating', 'Runtime', 'Studio', 'Tags',
        'Poster URL', 'Thumbnail URL', 'Cover Image', 'Trailer URL', 'Subtitle URL',
        'Video Links', 'Secure Video Links', 'Quality', 'Quality Label', 'Custom Quality Label', 'Audio Tracks',
        'TMDB ID', 'Total Seasons', 'Total Episodes', 'Created At', 'Updated At'
      ];

      const csvData = dataToExport.map(item => {
        // Handle seasons/episodes data for web series
        const seasonsInfo = item.type === 'series' && item.seasons ?
          item.seasons.map(season =>
            `Season ${season.seasonNumber}: ${season.episodes?.length || 0} episodes`
          ).join('; ') : '';

        // Combine all video links (both legacy and secure)
        const allVideoLinks = [
          item.videoLinks || '',
          item.secureVideoLinks || ''
        ].filter(link => link.trim()).join(' | ');

        return [
          escapeCsvValue(item.title),
          escapeCsvValue(item.description),
          escapeCsvValue(item.type === 'movie' ? 'Movie' : 'Web Series'),
          escapeCsvValue(item.category || ''),
          escapeCsvValue(item.section_ids?.join('; ') || ''), // Section IDs
          escapeCsvValue(item.year),
          escapeCsvValue(item.genres?.join('; ') || ''),
          escapeCsvValue(item.languages?.join('; ') || ''),
          escapeCsvValue(item.isPublished ? 'Published' : 'Draft'),
          escapeCsvValue(item.isFeatured ? 'Yes' : 'No'),
          escapeCsvValue(item.addToCarousel ? 'Yes' : 'No'),
          escapeCsvValue(item.imdbRating || ''),
          escapeCsvValue(item.runtime || ''),
          escapeCsvValue(item.studio || ''),
          escapeCsvValue(item.tags || ''),
          escapeCsvValue(item.posterUrl || item.image || ''),
          escapeCsvValue(item.thumbnailUrl || ''),
          escapeCsvValue(item.coverImage || ''),
          escapeCsvValue(item.trailer || ''),
          escapeCsvValue(item.subtitleUrl || ''),
          escapeCsvValue(item.videoLinks || ''),
          escapeCsvValue(item.secureVideoLinks || ''),
          escapeCsvValue(item.quality?.join('; ') || ''),
          escapeCsvValue(item.qualityLabel || 'none'), // Quality Label
          escapeCsvValue(item.customQualityLabel || ''), // Custom Quality Label
          escapeCsvValue(item.audioTracks?.join('; ') || ''),
          escapeCsvValue(item.tmdbId || ''),
          escapeCsvValue(item.totalSeasons || ''),
          escapeCsvValue(item.totalEpisodes || ''),
          escapeCsvValue(item.createdAt || ''),
          escapeCsvValue(item.updatedAt || '')
        ].join(',');
      });

      const csvContent = [csvHeaders.join(','), ...csvData].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'content-export.csv';
      a.click();
      URL.revokeObjectURL(url);
    } else {
      const jsonContent = JSON.stringify(dataToExport, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'content-export.json';
      a.click();
      URL.revokeObjectURL(url);
    }

    toast({
      title: "Export completed",
      description: `Data exported as ${format.toUpperCase()} successfully`,
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-primary">Manage Content</h2>
          <p className="text-muted-foreground">
            {totalItems} total items
            {selectedItems.length > 0 && ` • ${selectedItems.length} selected`}
          </p>
        </div>
        
        <div className="flex gap-2">
          {selectedItems.length > 0 && (
            <>
              <Button variant="outline" size="sm" onClick={handleBulkDelete}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Selected
              </Button>
              <Button variant="outline" size="sm" onClick={() => exportData('csv')}>
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
            </>
          )}
          <Button variant="outline" size="sm" onClick={() => exportData('csv')}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search content..."
            value={filters.search || ''}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            className="pl-10"
          />
        </div>
        
        <select
          value={filters.type || ''}
          onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value || undefined }))}
          className="h-10"
        >
          <option value="">All Types</option>
          <option value="movie">Movies</option>
          <option value="series">Web Series</option>
        </select>

        <select
          value={filters.featured === undefined ? '' : filters.featured.toString()}
          onChange={(e) => setFilters(prev => ({
            ...prev,
            featured: e.target.value === '' ? undefined : e.target.value === 'true'
          }))}
          className="h-10"
        >
          <option value="">All Featured</option>
          <option value="true">Featured</option>
          <option value="false">Not Featured</option>
        </select>

        <Button
          variant="outline"
          onClick={() => setFilters({})}
          disabled={Object.keys(filters).length === 0}
        >
          <Filter className="h-4 w-4 mr-2" />
          Clear
        </Button>
      </div>

      {/* Content Table */}
      <div className="border border-border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <input
                  type="checkbox"
                  checked={selectedItems.length === paginatedContent.length && paginatedContent.length > 0}
                  onChange={toggleSelectAll}
                  className="rounded"
                />
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('title')}
              >
                <div className="flex items-center gap-2">
                  Title
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('type')}
              >
                <div className="flex items-center gap-2">
                  Type
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('year')}
              >
                <div className="flex items-center gap-2">
                  Year
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>Genres</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Features</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="flex items-center justify-center gap-2">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    Loading content from database...
                  </div>
                </TableCell>
              </TableRow>
            ) : paginatedContent.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  No content found. Add some content to get started.
                </TableCell>
              </TableRow>
            ) : (
              paginatedContent.map((item) => (
              <TableRow key={item.id}>
                <TableCell>
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(item.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedItems(prev => [...prev, item.id]);
                      } else {
                        setSelectedItems(prev => prev.filter(id => id !== item.id));
                      }
                    }}
                    className="rounded"
                  />
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-10 h-15 object-cover rounded"
                    />
                    <div>
                      <div className="font-medium">{item.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {item.studio || 'Unknown Studio'}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={item.type === 'movie' ? 'default' : 'secondary'}>
                    {item.type === 'movie' ? 'Movie' : 'Web Series'}
                  </Badge>
                </TableCell>
                <TableCell>{item.year}</TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {item.genres.slice(0, 2).map(genre => (
                      <Badge key={genre} variant="outline" className="text-xs">
                        {genre}
                      </Badge>
                    ))}
                    {item.genres.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{item.genres.length - 2}
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={item.isPublished ? 'default' : 'secondary'}>
                    {item.isPublished ? 'Published' : 'Draft'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex gap-1">
                    <button
                      onClick={() => handleToggleFeature(item.id, 'isFeatured')}
                      className={`p-1 rounded ${item.isFeatured ? 'text-yellow-500' : 'text-muted-foreground'}`}
                      title="Toggle Featured"
                    >
                      <Star className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleToggleFeature(item.id, 'addToCarousel')}
                      className={`p-1 rounded ${item.addToCarousel ? 'text-blue-500' : 'text-muted-foreground'}`}
                      title="Toggle Carousel"
                    >
                      <Image className="h-4 w-4" />
                    </button>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex gap-2 justify-end">
                    <Button variant="ghost" size="sm" onClick={() => handleView(item)}>
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleEdit(item)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-destructive hover:text-destructive"
                      onClick={() => handleDelete(item.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} results
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    className="pagination-button"
                    onClick={() => handlePageChange(page)}
                  >
                    {page}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Dialogs */}
      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Content Details: {selectedContent?.title}</DialogTitle>
          </DialogHeader>
          {selectedContent && (
            <div className="space-y-6">
              {/* Basic Information */}
              <section className="space-y-4">
                <h3 className="text-lg font-semibold text-primary">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="font-medium">Title:</Label>
                    <p className="text-sm mt-1">{selectedContent.title}</p>
                  </div>
                  <div>
                    <Label className="font-medium">Type:</Label>
                    <p className="text-sm mt-1 capitalize">{selectedContent.type}</p>
                  </div>
                  <div>
                    <Label className="font-medium">Year:</Label>
                    <p className="text-sm mt-1">{selectedContent.year}</p>
                  </div>
                  <div>
                    <Label className="font-medium">TMDB ID:</Label>
                    <p className="text-sm mt-1">{selectedContent.tmdbId || 'Not set'}</p>
                  </div>
                  <div>
                    <Label className="font-medium">Studio:</Label>
                    <p className="text-sm mt-1">{selectedContent.studio || 'Not specified'}</p>
                  </div>
                  <div>
                    <Label className="font-medium">Runtime:</Label>
                    <p className="text-sm mt-1">{selectedContent.runtime ? `${selectedContent.runtime} minutes` : 'Not specified'}</p>
                  </div>
                  <div>
                    <Label className="font-medium">IMDb Rating:</Label>
                    <p className="text-sm mt-1">{selectedContent.imdbRating || 'Not rated'}</p>
                  </div>
                </div>

                {selectedContent.description && (
                  <div>
                    <Label className="font-medium">Description:</Label>
                    <p className="text-sm mt-1 leading-relaxed">{selectedContent.description}</p>
                  </div>
                )}
              </section>

              {/* Genres & Languages */}
              <section className="space-y-4">
                <h3 className="text-lg font-semibold text-primary">Genres & Languages</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="font-medium">Genres:</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedContent.genres && selectedContent.genres.length > 0 ? (
                        selectedContent.genres.map(genre => (
                          <Badge key={genre} variant="secondary">{genre}</Badge>
                        ))
                      ) : (
                        <span className="text-sm text-muted-foreground">No genres specified</span>
                      )}
                    </div>
                  </div>
                  <div>
                    <Label className="font-medium">Languages:</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedContent.languages && selectedContent.languages.length > 0 ? (
                        selectedContent.languages.map(language => (
                          <Badge key={language} variant="outline">{language}</Badge>
                        ))
                      ) : (
                        <span className="text-sm text-muted-foreground">No languages specified</span>
                      )}
                    </div>
                  </div>
                </div>

                {selectedContent.quality && selectedContent.quality.length > 0 && (
                  <div>
                    <Label className="font-medium">Quality:</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedContent.quality.map(quality => (
                        <Badge key={quality} variant="default">{quality}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </section>

              {/* Media & Links */}
              <section className="space-y-4">
                <h3 className="text-lg font-semibold text-primary">Media & Links</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="font-medium">Poster Image:</Label>
                    {selectedContent.posterUrl || selectedContent.image ? (
                      <div className="mt-2">
                        <img
                          src={selectedContent.posterUrl || selectedContent.image}
                          alt={selectedContent.title}
                          className="w-32 h-48 object-cover rounded-md border"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground mt-1">No poster image</p>
                    )}
                  </div>
                  <div>
                    <Label className="font-medium">Thumbnail Image:</Label>
                    {selectedContent.thumbnailUrl ? (
                      <div className="mt-2">
                        <img
                          src={selectedContent.thumbnailUrl}
                          alt={`${selectedContent.title} thumbnail`}
                          className="w-32 h-20 object-cover rounded-md border"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground mt-1">No thumbnail image</p>
                    )}
                  </div>
                </div>

                {selectedContent.trailer && (
                  <div>
                    <Label className="font-medium">Trailer URL:</Label>
                    <p className="text-sm mt-1 break-all">{selectedContent.trailer}</p>
                  </div>
                )}

                {selectedContent.videoLinks && (
                  <div>
                    <Label className="font-medium">Video Links:</Label>
                    <p className="text-sm mt-1 text-muted-foreground">Video links are available (hidden for security)</p>
                  </div>
                )}
              </section>

              {/* Publishing Status */}
              <section className="space-y-4">
                <h3 className="text-lg font-semibold text-primary">Publishing Status</h3>
                <div className="flex flex-wrap gap-4">
                  <div>
                    <Label className="font-medium">Published:</Label>
                    <Badge variant={selectedContent.isPublished ? "default" : "secondary"} className="ml-2">
                      {selectedContent.isPublished ? "Yes" : "No"}
                    </Badge>
                  </div>
                  <div>
                    <Label className="font-medium">Featured:</Label>
                    <Badge variant={selectedContent.isFeatured ? "default" : "secondary"} className="ml-2">
                      {selectedContent.isFeatured ? "Yes" : "No"}
                    </Badge>
                  </div>
                  <div>
                    <Label className="font-medium">In Carousel:</Label>
                    <Badge variant={selectedContent.addToCarousel ? "default" : "secondary"} className="ml-2">
                      {selectedContent.addToCarousel ? "Yes" : "No"}
                    </Badge>
                  </div>
                </div>

                {selectedContent.tags && (
                  <div>
                    <Label className="font-medium">Tags:</Label>
                    <p className="text-sm mt-1">{selectedContent.tags}</p>
                  </div>
                )}
              </section>

              {/* Metadata */}
              <section className="space-y-4">
                <h3 className="text-lg font-semibold text-primary">Metadata</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                  <div>
                    <Label className="font-medium">Created:</Label>
                    <p className="mt-1">{selectedContent.createdAt ? new Date(selectedContent.createdAt).toLocaleString() : 'Not available'}</p>
                  </div>
                  <div>
                    <Label className="font-medium">Last Updated:</Label>
                    <p className="mt-1">{selectedContent.updatedAt ? new Date(selectedContent.updatedAt).toLocaleString() : 'Not available'}</p>
                  </div>
                </div>
              </section>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <ContentEditDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        content={selectedContent}
        onSave={handleSaveContent}
      />
    </div>
  );
}

import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Play, AlertTriangle, Loader2 } from 'lucide-react';
import {
  decodeVideoLinks,
  parseVideoLinks,
  generatePlayerNames
} from '@/utils/videoSecurity';

// Universal Iframe component that accepts ANY embed link
interface UniversalIframeProps {
  src: string;
  title: string;
  className: string;
  allowFullScreen: boolean;
  allow: string;
  loading?: string;
  referrerPolicy: string;
  sandbox: string;
  onLoad?: () => void;
  onError?: () => void;
}

function UniversalIframe({
  src,
  title,
  className,
  allowFullScreen,
  onLoad,
  onError
}: UniversalIframeProps) {
  return (
    <iframe
      src={src}
      title={title}
      className={className}
      allowFullScreen={allowFullScreen}
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen; web-share"
      loading="eager"
      referrerPolicy="no-referrer-when-downgrade"
      style={{ border: 'none', width: '100%', height: '100%' }}
      onLoad={onLoad}
      onError={onError}
    />
  );
}

interface SecureVideoPlayerProps {
  /** Encoded video links string */
  encodedVideoLinks?: string;
  /** Fallback for legacy videoLinks field */
  legacyVideoLinks?: string;
  /** Title for the player */
  title?: string;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show player selection buttons */
  showPlayerSelection?: boolean;
  /** Enable dynamic aspect ratio detection (default: false for backward compatibility) */
  enableDynamicAspectRatio?: boolean;
  /** Force mobile-friendly aspect ratios */
  forceMobileFriendly?: boolean;
  /** Override aspect ratio detection with specific ratio */
  preferredAspectRatio?: string;
  /** Enable responsive aspect ratio adjustments */
  enableResponsive?: boolean;
}

export default function SecureVideoPlayer({
  encodedVideoLinks,
  legacyVideoLinks,
  title = "Video Player",
  className = "",
  showPlayerSelection = true,
  enableDynamicAspectRatio = false,
  forceMobileFriendly = false,
  preferredAspectRatio,
  enableResponsive = true
}: SecureVideoPlayerProps) {
  const [selectedPlayerIndex, setSelectedPlayerIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPlayerSwitching, setIsPlayerSwitching] = useState(false);

  // Decode and parse video links - UNIVERSAL COMPATIBILITY
  const videoLinksData = useMemo(() => {
    try {
      setIsLoading(true);
      setError(null);

      let rawLinks = '';

      // Try to decode secure links first, fallback to legacy
      if (encodedVideoLinks) {
        rawLinks = decodeVideoLinks(encodedVideoLinks);
      } else if (legacyVideoLinks) {
        rawLinks = legacyVideoLinks;
        console.warn('Using legacy video links - consider updating to secure format');
      }

      if (!rawLinks) {
        setError('No video links available');
        return { links: [], playerNames: [] };
      }

      const links = parseVideoLinks(rawLinks);

      if (links.length === 0) {
        setError('No valid video links found');
        return { links: [], playerNames: [] };
      }

      // UNIVERSAL COMPATIBILITY - Accept iframe tags and direct URLs
      const validLinks = links.filter((link, index) => {
        const trimmedLink = link?.trim();
        if (!trimmedLink) {
          console.warn(`Link ${index + 1} is empty`);
          return false;
        }
        
        // Check if it's an iframe HTML tag
        if (trimmedLink.toLowerCase().includes('<iframe')) {
          const srcMatch = trimmedLink.match(/src\s*=\s*["']([^"']+)["']/i);
          if (srcMatch && srcMatch[1]) {
            console.log(`ACCEPTED Link ${index + 1} (iframe tag):`, srcMatch[1].substring(0, 50) + '...');
            return true;
          } else {
            console.warn(`Link ${index + 1} is iframe tag but no src found:`, trimmedLink.substring(0, 100));
            return false;
          }
        }
        
        // Check if it's a direct URL
        if (trimmedLink.startsWith('http') || trimmedLink.startsWith('//')) {
          console.log(`ACCEPTED Link ${index + 1} (direct URL):`, trimmedLink.substring(0, 50) + '...');
          return true;
        }
        
        console.warn(`Link ${index + 1} is neither iframe tag nor direct URL:`, trimmedLink.substring(0, 100));
        return false;
      });

      console.log(`Processed ${links.length} links, ${validLinks.length} accepted (no restrictions)`);

      if (validLinks.length === 0) {
        setError('No video links found');
        return { links: [], playerNames: [] };
      }

      const playerNames = generatePlayerNames(validLinks);

      return {
        links: validLinks,
        playerNames
      };
    } catch (err) {
      console.error('Error processing video links:', err);
      setError('Failed to load video player');
      return { links: [], playerNames: [] };
    } finally {
      setIsLoading(false);
    }
  }, [encodedVideoLinks, legacyVideoLinks]);

  // Extract actual URL from iframe HTML tags or use direct URLs
  const currentUrl = useMemo(() => {
    if (videoLinksData.links.length === 0 || selectedPlayerIndex >= videoLinksData.links.length) {
      return '';
    }
    
    const rawLink = videoLinksData.links[selectedPlayerIndex].trim();
    
    // Check if it's an HTML iframe tag
    if (rawLink.toLowerCase().includes('<iframe')) {
      // Extract src attribute from iframe tag
      const srcMatch = rawLink.match(/src\s*=\s*["']([^"']+)["']/i);
      if (srcMatch && srcMatch[1]) {
        const extractedUrl = srcMatch[1].trim();
        console.log('Extracted URL from iframe tag:', extractedUrl);
        return extractedUrl;
      }
    }
    
    // If it's already a direct URL, use it as is
    if (rawLink.startsWith('http') || rawLink.startsWith('//')) {
      console.log('Using direct URL:', rawLink);
      return rawLink;
    }
    
    console.warn('Could not extract valid URL from:', rawLink.substring(0, 100));
    return '';
  }, [videoLinksData.links, selectedPlayerIndex]);

  // Calculate aspect ratio configuration for current video
  const aspectRatioConfig = useMemo(() => {
    // Return default 16:9 configuration for universal compatibility
    return {
      aspectRatio: { className: 'aspect-video' },
      responsive: { combined: 'aspect-video' },
      platform: 'universal'
    };
  }, []);

  // Universal iframe configuration - No restrictions
  const iframeConfig = useMemo(() => {
    // Universal configuration that works with ANY embed link
    return {
      sandbox: "", // No sandbox restrictions
      referrerPolicy: "no-referrer-when-downgrade",
      allow: "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen; web-share",
      loading: "eager"
    };
  }, []);

  // Reset selected player when links change
  useEffect(() => {
    if (videoLinksData.links.length > 0 && selectedPlayerIndex >= videoLinksData.links.length) {
      setSelectedPlayerIndex(0);
    }
  }, [videoLinksData.links.length, selectedPlayerIndex]);

  // Handle player switching with loading state
  const handlePlayerSwitch = (newIndex: number) => {
    if (newIndex !== selectedPlayerIndex) {
      setIsPlayerSwitching(true);
      setSelectedPlayerIndex(newIndex);
      // Clear switching state very quickly for superfast switching
      setTimeout(() => {
        setIsPlayerSwitching(false);
      }, 50);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={`bg-background border border-border rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-center space-x-2">
          <Loader2 className="w-5 h-5 animate-spin" />
          <span className="text-muted-foreground">Loading player...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error || videoLinksData.links.length === 0) {
    return (
      <div className={`bg-background border border-border rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-center space-x-2 text-destructive">
          <AlertTriangle className="w-5 h-5" />
          <span>{error || 'No video available'}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-background border border-border rounded-lg overflow-hidden ${className}`}>
      {/* Player Selection */}
      {showPlayerSelection && videoLinksData.links.length > 1 && (
        <div className="p-4 border-b border-border bg-muted/50">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-foreground">Select Player</h3>
            <Badge variant="outline" className="text-xs">
              {videoLinksData.links.length} available
            </Badge>
          </div>
          <div className="flex flex-wrap gap-2">
            {videoLinksData.playerNames.map((name, index) => (
              <Button
                key={index}
                variant={selectedPlayerIndex === index ? "default" : "outline"}
                size="sm"
                onClick={() => handlePlayerSwitch(index)}
                className="text-xs"
                disabled={isPlayerSwitching}
              >
                <Play className="w-3 h-3 mr-1" />
                {name}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Video Player */}
      <div className="relative">
        <div className={`video-player-container ${enableResponsive ? aspectRatioConfig.responsive.combined : aspectRatioConfig.aspectRatio.className} ${forceMobileFriendly ? 'mobile-friendly' : ''} bg-black mobile-responsive-player`}>
          {/* Player switching loading overlay */}
          {isPlayerSwitching && (
            <div className="absolute top-2 right-2 bg-black/60 rounded px-2 py-1 z-10">
              <div className="flex items-center space-x-1 text-white">
                <Loader2 className="w-3 h-3 animate-spin" />
                <span className="text-xs">Switching...</span>
              </div>
            </div>
          )}
          
          {/* Universal Video Player - Works with ANY embed link */}
          {currentUrl ? (
            <UniversalIframe
              key={`player-${selectedPlayerIndex}`}
              src={currentUrl}
              title={`${title} - Player ${selectedPlayerIndex + 1}`}
              className="w-full h-full"
              allowFullScreen
              allow={iframeConfig.allow}
              loading={iframeConfig.loading}
              referrerPolicy={iframeConfig.referrerPolicy as any}
              sandbox={iframeConfig.sandbox}
              onLoad={() => {
                console.log(`Universal player loaded successfully:`, currentUrl.substring(0, 50) + '...');
                setIsPlayerSwitching(false);
              }}
              onError={() => {
                console.log(`Player load attempt for:`, currentUrl.substring(0, 50) + '...');
                setIsPlayerSwitching(false);
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <AlertTriangle className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>Could not extract valid URL from embed code</p>
                <p className="text-xs mt-2">Raw data: {videoLinksData.links[selectedPlayerIndex]?.substring(0, 100)}...</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Player Info */}
      {title && (
        <div className="p-3 bg-muted/30">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-foreground">{title}</span>
            <div className="flex items-center gap-2">
              {videoLinksData.playerNames[selectedPlayerIndex] && (
                <Badge variant="secondary" className="text-xs">
                  {videoLinksData.playerNames[selectedPlayerIndex]}
                </Badge>
              )}
              <Badge variant="outline" className="text-xs">
                Universal Player
              </Badge>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}